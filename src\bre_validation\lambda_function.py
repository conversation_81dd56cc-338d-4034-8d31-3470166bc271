import os
import json

from .validation_helper.validation_execution import ValidatorExecution
from ..common.aria_helper.boto3_utils import get_secret, trigger_lambda_response
from ..common.aria_helper.mongo_utils import Mongo
from ..common.aria_helper.aria_utils import ARIA

environment = os.environ['ENV']
database_name = os.environ['DATABASE_NAME']
aria_environment = os.environ['ARIA_ENVIRONMENT']
common_prefix = f"{os.environ['ENV']}-{os.environ['PROJECT_NAME']}"


class BreValidationHandler:
    def __init__(self, event):
        self.event = event
        # Parse the event body once and reuse it
        self.document = event['body'] if isinstance(event['body'], dict) else json.loads(event['body'])
        self.input_body = self.document
        self.app_id = self.document['app_id']
        self.statuses = self.input_body['aria_status']
        self.mongo_client = Mongo(get_secret(common_prefix + '-mongodb_uri', return_json=False)).client
        self.validator_execution = ValidatorExecution()
        self.aria_secret = get_secret(secret_name=f'{environment}-Connector-aria_creds')
        self.validation_config = self.mongo_client[database_name]["validation_config"].find_one({
            "app_id": self.app_id,
            "validation_type_key": "tag_titles"
        })
        
        if not self.validation_config:
            raise ValueError(f"No validation config found for app_id: {self.app_id}")
    def post_to_aria(self, bre_response):
        """
        Post the BRE response to ARIA.
        Returns True if successful, raises exception if failed.
        """
        try:
            aria = ARIA(
                base_url=self.aria_secret[aria_environment]['url'],
                request_token=self.aria_secret[aria_environment]['token']
            )

            aria.bre_reply(
                app_id=self.document['app_id'],
                item_id=self.document['id'],
                bre_response=bre_response
            )
            return True
        except Exception as e:
            print(f"Failed to post to ARIA: {str(e)}")
            raise

    def run(self):
        try:
            # Perform validation
            print("Performing validation")
            print("document--------" ,self.document)
            print(self.validation_config)
            is_valid, validation_result = self.validator_execution.validate_data(self.document, self.validation_config)

            # Build payload for bre_post
            bre_response = {
                "aria_status": {"value": self.statuses},
                "validation_result": {"value": validation_result},
                "is_valid": {"value": is_valid},
                "bre_type": {"value": "validation"}
            }

            self.post_to_aria(bre_response)
            print("bre_response-------------", bre_response)
            return {
                "statusCode": 200,
                "body": json.dumps({
                    "message": "BRE response posted successfully",
                    "validation_passed": is_valid,
                    "bre_type": "validation"
                })
            }

        except Exception as e:
            return {
                "statusCode": 500,
                "body": json.dumps(f"Validation Lambda Error: {str(e)}")
            }


def lambda_handler(event, context=None):
    print("event-data",event)
    if 'body' not in list(event.keys()):
        raise ValueError("body tag is missing on the dict. Skipping...")

    bre_validation_handler = BreValidationHandler(event)
    return bre_validation_handler.run()
